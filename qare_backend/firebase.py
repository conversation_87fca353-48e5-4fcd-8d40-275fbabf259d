import firebase_admin
from firebase_admin import credentials, db

# Initialize Firebase
cred = credentials.Certificate("firebase_cred.json")
firebase_admin.initialize_app(cred, {
    'databaseURL': 'https://qareai-react-default-rtdb.asia-southeast1.firebasedatabase.app/'
})

def format_email(email):
    return email.replace("@", "_at_").replace(".", "_dot_")

def save_new_patient_report(doctors_id, patient_id, report_key, report_data):
    try:
        formatted_id = format_email(doctors_id)
        ref_path = f'Doctors_id/{formatted_id}/patient_ids/{patient_id}/report/{report_key}'
        ref = db.reference(ref_path)
        ref.update(report_data)
        print("✅ New patient report saved:", report_data)
    except Exception as e:
        print("❌ Error saving patient report:", e)
