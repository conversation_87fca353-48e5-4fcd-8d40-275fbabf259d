
from pydantic import BaseModel, Field
from typing import List, Optional

class Quesnare_var_type(BaseModel):
    question: str
    choices: List[str]
    end_convo: bool
    question_type : str
    
class Doc_var_type(BaseModel):
    question: str
    choices: List[str]
    question_type : str
       
class Report_var_type(BaseModel):
    patient_details: list[str] = Field(description="Overview of the patient in bulleted points in a list")
    symptoms: list[str] = Field(description="List of symptoms")
    symptoms_explanation: str = Field(description="Explanation of the symptoms")
    medication_history: list[str] = Field(description="List of any past medications, if none return ['']")
    possible_differentials: list[str] = Field(description="Possible conditions or diagnoses")
    next_steps: str = Field(description="Recommended next actions")
    
class Differential_var_type(BaseModel):
    differentials : List[str] = Field(description="A list of top 5 possible disease names.")
    ranking: List[int] = Field(description="A list of ranks (integers), where the rank corresponds to the disease in the same index")
    
class MedicalSummary(BaseModel):
    chief_complaint: str = Field(description="The main reason the patient came in, typically in their own words.")
    history_of_present_illness: str = Field(description="Detailed history and progression of the current issue.")
    past_medical_history: str = Field(description="Relevant past illnesses, chronic conditions, or surgeries.")
    medications: str = Field(description="Current medications the patient is taking, including dosage if mentioned.")
    vitals_and_examination: str = Field(description="Vital signs and physical examination findings.")
    assessment: str = Field(description="The clinician's impression, differential diagnosis, or clinical reasoning.")
    plan: str = Field(description="Proposed treatment, medications, lifestyle advice, or referrals.")
    investigations: str = Field(description="Tests that were ordered or reviewed during the visit.")
    follow_up_instructions: str = Field(description="Instructions given to the patient regarding when to return or watch for symptoms.")
    
class DischargeSummary(BaseModel):
    # 1. Header Section
    patient_uhid: str = Field( description="Unique Hospital ID of the patient")
    patient_name: str = Field( description="Full name of the patient")
    patient_age: Optional[str] = Field( description="Age of the patient")
    patient_gender: Optional[str] = Field( description="Gender of the patient")
    admission_number: str = Field( description="Hospital admission number")
    admission_datetime: str = Field( description="Date and time of admission")
    patient_address: Optional[str] = Field( description="Residential address of the patient")
    discharge_status: str = Field( description="Discharge mode (e.g., Normal, AMA, Transferred)")

    # 2. Consultant Info
    treating_consultant: str = Field( description="Name of the treating doctor")
    consultant_specialty: Optional[str] = Field( description="Specialization of the doctor")

    # 3. Admission Details
    provisional_diagnosis: Optional[str] = Field( description="Initial diagnosis at admission")
    presenting_complaints: list[str] = Field( description="Presenting symptoms and complaints with duration")
    reason_for_admission: Optional[str] = Field( description="Reason for hospital admission")
    physical_examination: list[str] = Field( description="Key physical exam findings at admission")
    general_findings: list[str] = Field( description="General findings: Eyes, Neck, Throat, Head, Heart etc.")

    # 4. Hospitalization Details
    investigations_summary: list[str] = Field( description="Key investigations during hospitalization")
    hospital_course: str = Field( description="Summary of clinical course including complications")
    treatment_summary: list[str] = Field( description="Summary of treatments given in hospital")

    # 5. Surgery Details
    surgery_performed: bool = Field( description="True if surgery was performed, else False")
    surgery_notes: Optional[str] = Field( description="Detailed surgical notes")
    anesthetist_name: Optional[str] = Field( description="Name of the anesthetist involved")
    anesthesia_type: Optional[str] = Field( description="Type of anesthesia used")

    # 6. Discharge Details
    final_diagnosis: str = Field( description="Confirmed final diagnosis at discharge")
    discharge_prescription: list[str] = Field( description="Medications to be followed after discharge")
    discharge_advice: list[str] = Field( description="General advice post discharge like rest, diet, etc.")
    follow_up_date: Optional[str] = Field( description="Date for next follow-up visit")
    emergency_contact: Optional[str] = Field( description="Emergency contact number or person")

    # 7. Signoff Section
    doctor_signatory: Optional[str] = Field( description="Signature or name of the treating doctor")
    patient_or_attendant_signatory: Optional[str] = Field( description="Signature of patient or family member")    
    