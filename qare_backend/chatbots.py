from groq import Groq
import os
from pydantic import BaseModel, Field
import instructor
from typing import List, Union, Optional
import json
from typing import Dict
from datetime import datetime
from pydants import Quesnare_var_type, Doc_var_type, Report_var_type, Differential_var_type, MedicalSummary, DischargeSummary


os.environ['GROQ_API_KEY'] = "********************************************************"
client = Groq()
client = instructor.patch(client)



def MetaData_agent(chat):
    print('RECEVIED METAAAA')
    print(chat)
    prompt = 'You are an experienced history-taking nurse. Your task is to collect the most relevant patient history efficiently using a structured question flow. Your goal is to reach a diagnosis in 7 questions or fewer Ask only one concise question at a time based on the patients previous response. You can ask a maximum of 7 questions. Prioritize MCQ format, but use text input if necessary.Do Not repeat question. Minimize the number of questions while covering essential aspects: Symptom Onset, Severity, Duration Medical History Lifestyle Factors Allergies Avoid asking for information the patient is unlikely to know. When the history is complete, set "end_convo": true; otherwise, keep it false. Return the response in valid JSON format with the following structure: {"question": "Your next question here", "choices": ["Option 1", "Option 2", "Option 3"], "question_type": "single_select_MCQ" or "multi_select_MCQ" or "text" "end_convo": false  // Set to true when history is complete} Stay precise and efficient. Goal: Complete History taking as efficiently as possible with a maximum of 7 questions. Be precise, strategic, and outcome-focused.'
    chat_history = [{"role": "system", "content": prompt},{"role": "user", "content": chat}]
    response = client.chat.completions.create(
        model="llama-3.3-70b-versatile",
        messages=chat_history,
        response_model=Quesnare_var_type,
        temperature=0.1
    )
    return response

def Doc_agent(chat, differentials):
    print('RECEVIED DOCCC')
    print(chat, differentials)
    prompt = f'''You are a highly skilled doctor aiming to confirm or rule out the following differentials as efficiently as possible:
- Given differentials: {differentials}

**Instructions:**
- Ask only high-yield questions that directly rule in or rule out these conditions.
- Focus exclusively on confirming at least 3 out of the 5 differentials.
- Use MCQ format whenever possible for quick narrowing.
- If an MCQ format is not applicable, allow text input.
- Do not ask broad or irrelevant questions.
- **Once 3 out of 5 differentials are confirmed, stop questioning.**

**Response Format:**
Return a JSON object with the following fields:
- `question` (string): the question to ask
- `choices` (array of strings): the possible answers
- `question_type` (string): one of `"single_select_MCQ"`, `"multi_select_MCQ"`, or `"text"`

Example:

```json
{{
  "question": "What is your smoking status?",
  "choices": ["Smoker", "Non-smoker", "Former smoker"],
  "question_type": "single_select_MCQ"
}}
    '''
    chat_history = [{"role": "system", "content": prompt}, {"role": "user", "content": chat}]

    response = client.chat.completions.create(
        model="llama-3.3-70b-versatile",
        messages=chat_history,
        response_model=Union[Doc_var_type, List[Doc_var_type]],
        temperature=0.1
    )
    if isinstance(response, list):
        print(f"Warning: Multiple responses detected! Using the first one: {response}")
        response = response[0]  # Pick the first response

    return response

def Differentials_agent(history):
    prompt = "You are an expert and experienced from the healthcare and biomedical domain with extensive medical knowledge and practical experience. You need to return the top 5 possible differential diagnosis given the patient profile and symptoms. In your answer, leverage your deep medical expertise such as relevant anatomical structures, physiological processes, diagnostic criteria, treatment guidelines, or other pertinent medical concepts. Use precise medical terminology while answering. You only need to return diseases names"
    system_prompt = [{"role": "system", "content": prompt},{"role": "user", "content": history}]
    response = client.chat.completions.create(
        model="llama-3.3-70b-versatile",
        messages=system_prompt,
        temperature=0.1
    )
    return response.choices[0].message.content

def Differentials_ranking_agent(history):
    prompt = """You are an expert clinician specializing in differential diagnosis.
    Given the patient's history and symptoms, return the **top 5 possible differential diagnoses** ranked by probablity.

    **Response Format:**
    - Provide two separate lists:
      1. **differentials**: A list of disease names.
      2. **ranking**: A list of ranks (integers), where the rank corresponds to the disease in the same index"""

    system_prompt = [{"role": "system", "content": prompt},{"role": "user", "content": history}]
    response = client.chat.completions.create(
        model="llama-3.3-70b-versatile",
        messages=system_prompt,
        temperature=0.1,
        response_model= Differential_var_type
    )
    return response

def generate_report(chat, Possible_differentials):
    ext = chat
    ext += f' The Medical bot came up with the following differentials {Possible_differentials}'

    prompt = """
    You are a medical AI tasked with generating a structured patient report in **JSON format**.

    - **Response Format:** Return a JSON object with the following keys:
      - `"patient_details"` (list) → Overview of the patient in bulleted points in a list.
      - `"symptoms"` (list) → List of symptoms
      - `"symptoms_explanation"` (string) → Explanation of the symptoms
      - `"medication_history"` (list) → List of any past medications, if none return [""] (not an empty list [])
      - `"possible_differentials"` (list) → Possible conditions or diagnoses
      - `"next_steps"` (string) → Recommended next actions

    - **Example JSON Response:**
    {
      "patient_details": ["Name: John Doe", "Age: 45", "Sex: Male","Diabetic: Yes/No", "Obese: Yes/No"......]
      "symptoms": ["Fever", "Cough", "Shortness of breath"],
      "symptoms_explanation": "The patient reports a high fever for 3 days along with a worsening cough...",
      "medication_history": ["Lisinopril for hypertension", "Metformin for diabetes"],
      "possible_differentials": ["Pneumonia", "COVID-19", "Bronchitis"],
      "next_steps": "Recommend further tests such as CBC and Chest X-ray."
    }
    **Return only valid JSON. Do not include extra text.**
    """

    system_prompt = {"role": "system", "content": prompt}
    chat_history = [system_prompt]
    chat_history.append({"role": "user", "content": ext})

    response = client.chat.completions.create(
        model="llama-3.3-70b-versatile",
        messages=chat_history,
        response_model = Report_var_type
    )
    return response

def edit_report(past_report, commands):
    prompt = f"""
    Given the following medical report in JSON format, modify it based on the instructions in the transcription text.

    **Medical Report (JSON):**
    {json.dumps(past_report, indent=2)}

    **Instructions (Transcription):**
    {commands}

    Return only the updated JSON without additional text.
    """

    response = client.chat.completions.create(
        model="llama-3.3-70b-versatile",
        messages=[
            {"role": "system", "content": "You are a medical AI assistant."},
            {"role": "user", "content": prompt}
        ],
        response_model = MedicalSummary,
        temperature=0.0
    )
    return response

def generate_medical_summary(transcript):
    prompt = """
You are a clinical AI assistant. Based on the transcript of a conversation between a doctor and a patient, generate a structured summary in **valid JSON format** following the schema below.

**Output Format:**
{
    "chief_complaint": "",
    "history_of_present_illness": "",
    "past_medical_history": "",
    "medications": "",
    "vitals_and_examination": "",
    "assessment": "",
    "plan": "",
    "investigations": "",
    "follow_up_instructions": ""
}

**Instructions:**
- Extract only the information actually mentioned in the transcript. **Do not infer or hallucinate details.**
- If any section is not discussed or mentioned, write: **"No information available"**.
- Be concise and clinically accurate.
- Return only a valid JSON. No markdown, explanation, or extra text.
- Translate the conversation into a structured medical summary in English.

"""

    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": str(transcript)}
    ]

    response = client.chat.completions.create(
        model="llama-3.3-70b-versatile",
        messages=messages,
        response_model=MedicalSummary
    )
    return response



def generate_discharge_summary_from_documents(documents):
    # Import DischargeSummary at the beginning of the function to ensure it's available
    from pydants import DischargeSummary

    # Combine all documents based on their type into a readable report
    compiled_report = ""
    for key, content in documents.items():
        try:
            serial, filetype = key.split("_", 1)
        except ValueError:
            filetype = "Unknown Document"
        compiled_report += f"\n\n=== {filetype.upper()} ===\n{content.strip()}\n"

    # Prompt
    prompt = f"""
You are a clinical AI assistant. Based on the text extracted from various hospital documents, generate a structured Discharge Summary in **valid JSON format** following the schema below.

### Pydantic Schema: (Use these fields in the response)
{DischargeSummary.schema_json(indent=2)}

### Instructions:
- Use only the information available in the documents.
- Do **not** infer or fabricate any information.
- Be concise, clinically accurate, and return only a valid JSON object.
- IMPORTANT: You MUST provide values for ALL required fields. Do not leave any required field as null or None.
- For required fields where information is not available, use appropriate default values:
  - For string fields: Use "Not specified" or "Unknown"
  - For list fields: Use empty lists []
  - For boolean fields: Use false
- Specifically ensure that 'treating_consultant' and 'general_findings' fields are properly populated.
- Translate medical content into structured discharge format in **English**.

### Input Documents:
{compiled_report}
"""

    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": 'Generate discharge summary from the above documents.'}
    ]

    # LLM Call with enforced Pydantic output schema
    try:
        response = client.chat.completions.create(
            model="llama-3.3-70b-versatile",
            messages=messages,
            response_model=DischargeSummary
        )
        return response
    except Exception as e:
        print(f"Error in LLM response: {e}")
        # If the LLM fails to generate a valid response, create a minimal valid response

        # Create a minimal valid discharge summary with required fields
        minimal_summary = {
            "patient_uhid": "Unknown",
            "patient_name": "Unknown",
            "admission_number": "Unknown",
            "admission_datetime": "Unknown",
            "discharge_status": "Unknown",
            "treating_consultant": "Unknown Doctor",
            "presenting_complaints": [],
            "physical_examination": [],
            "general_findings": [],
            "investigations_summary": [],
            "hospital_course": "Unknown",
            "treatment_summary": [],
            "surgery_performed": False,
            "final_diagnosis": "Unknown",
            "discharge_prescription": [],
            "discharge_advice": []
        }

        # Try to extract information from documents to populate the minimal summary
        for key, content in documents.items():
            if "DoctorProgressNote" in key or "DischargeNote" in key:
                for line in content.split("\n"):
                    if "Diagnosis" in line and minimal_summary["final_diagnosis"] == "Unknown":
                        minimal_summary["final_diagnosis"] = line.split(":", 1)[1].strip() if ":" in line else line.strip()

            if "AdmissionNote" in key:
                for line in content.split("\n"):
                    if "Patient Name:" in line:
                        minimal_summary["patient_name"] = line.split(":", 1)[1].strip()
                    elif "UHID:" in line:
                        minimal_summary["patient_uhid"] = line.split(":", 1)[1].strip()
                    elif "Admission Number:" in line:
                        minimal_summary["admission_number"] = line.split(":", 1)[1].strip()
                    elif "Admitted on:" in line:
                        minimal_summary["admission_datetime"] = line.split(":", 1)[1].strip()

        return DischargeSummary(**minimal_summary)




# def PatChat_agent(chat_history, patient_history):
#     prompt = f"You are a helpful and precise medical assistant with access to the patient's history and past reports. Your primary role is to provide accurate answers based strictly on the available patient data. Guidelines:- Answer only based on the given patient information.- Be concise and to the point.- Do not add extra details beyond what is available.- If the requested information is not in the provided patient history, clearly state: 'I don't know.'- Never hallucinate or make assumptions. Patient Information: {patient_history}"
#     system_prompt = {"role": "system", "content": prompt}
#     if chat_history[0]['role'] != 'system':
#         chat_history.insert(0, system_prompt)
#     response = client.chat.completions.create(
#         model="llama-3.3-70b-versatile",
#         messages=chat_history,
#     )
#     return response.choices[0].message.content

# def get_info_agent(content):
#     prompt = """You are a concise and clear medical term explainer. Your role is to assist patients by providing simple, easy-to-understand explanations of medical terms or phrases they may not be familiar with.
#     Guidelines:
#     - Keep it brief (1-2 sentences).
#     - Use simple language (avoid medical jargon unless necessary).
#     - Stay neutral and reassuring (avoid alarming language).
#     - Provide examples if helpful (e.g., "High blood pressure, also called hypertension, means the force of blood against your arteries is too high, like water in a hose under too much pressure.").
#     - Avoid giving medical advice (stick to definitions, not treatments).
#     - Your goal is to help patients quickly grasp the meaning of the term in a way that reduces confusion and anxiety."""

#     system_prompt = [{"role": "system", "content": prompt},{"role": "user", "content": content}]
#     response = client.chat.completions.create(
#         model="llama-3.3-70b-versatile",
#         messages=system_prompt,
#         temperature=0.1
#     )
#     return response.choices[0].message.content