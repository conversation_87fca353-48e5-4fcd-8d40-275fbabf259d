
from twilio.rest import Client
import json

#qare2 proj   join easy-mine
account_sid='**********************************'
auth_token='22990fbfa01c0071a56511c25f403f8e'




template_map = {
    'Apppointment':'HX2577ba02ea8a53b98011e9ffe80213e4',
    2: 'HXafb1f526932837c89a8988c576759c77',
    3: 'HX659cf101288d2f3e31d8ec305c3515ff',
    4: 'HX397e014ea45e3b8f6c5bce1ffadf2cea', 
    5: 'HX6c60a124e6e7901a1a0af53af25278c5',
    6: 'HX53c9e1f213afc5a811c261c2ff1e02be',
}
client = Client(account_sid, auth_token)
TWILIO_WHATSAPP_NUMBER = "whatsapp:+************"


def send_remider(PATIENT_NUMBER, Patientdata):

    try:
        client.messages.create(
            from_='whatsapp:+************',
            to=PATIENT_NUMBER,
            content_sid=template_map['Apppointment'],
            content_variables=json.dumps({
        '1': Patientdata['Name'],
        '2': Patientdata['Doctor'],
        '3': Patientdata['AppointmentTime'].split('T')[0],
        '4': Patientdata['AppointmentTime'].split('T')[1]
    }))
    except Exception as e:
        print(e)
        pass
    

def send_message(PATIENT_NUMBER, message):
    
    client.messages.create(
        body=message,
        from_=TWILIO_WHATSAPP_NUMBER,
        to=PATIENT_NUMBER
    )

 
emojis = ['1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣', '7️⃣', '8️⃣', '9️⃣', '🔟']

def send_ques(PATIENT_NUMBER, Ques, choices, ques_type):
    body = "🧑‍⚕️ Pre-consultation Ques:\n"
    body += f"{Ques}\n"
    

    
    if ques_type in ['single_select_MCQ', 'multi_select_MCQ']:

        if ques_type == 'single_select_MCQ':
            try:
                template_to_use = template_map[len(choices)]
                client.messages.create(
                    from_='whatsapp:+************',
                    to=PATIENT_NUMBER,
                    content_sid=template_to_use,
                    content_variables=json.dumps({
                        "1": Ques,
                        **{f"{i}": choice for i, choice in enumerate(choices, start=2)}
                    }))
                return
            except:
                choice_lines = [f"{emojis[i+1]} {choice}" for i, choice in enumerate(choices)]
                choice_text = "\n".join(choice_lines)
                body += f"{choice_text}\n\n"
                body += "Reply with the number corresponding to your choice (e.g. '1')"
        else:
            choice_lines = [f"{emojis[i]} {choice}" for i, choice in enumerate(choices)]
            choice_text = "\n".join(choice_lines)
            body += f"{choice_text}\n\n"
            body += "Reply with the numbers of your choices separated by commas (e.g. '1,3,4')"
    
    elif ques_type == 'text':
        body += "\nPlease type your response in a short sentence."

    # Send the message via Twilio
    client.messages.create(
        body=body,
        from_=TWILIO_WHATSAPP_NUMBER,
        to=PATIENT_NUMBER
    )


def update_chat_string(session,message_body,from_number):
    choice_received = message_body
    if session['chat_history']['question_type'][-1] == 'single_select_MCQ':
        try:
            choice_received = session['chat_history']['choice'][-1][int(choice_received) - 2]
        except:
            send_message(from_number, "Please reply with a number")
            return
    elif session['chat_history']['question_type'][-1] == 'multi_select_MCQ':
        try:
            choice_received = [session['chat_history']['choice'][-1][int(i) - 1] for i in choice_received.split(",")]
        except:
            send_message(from_number, "Please reply with a number in the format 1,2,3")
            return
    elif session['chat_history']['question_type'][-1] == 'text':
        choice_received = message_body
        
    session['chat_string'] += ", " +session['chat_history']['question'][-1]+" : "+str(choice_received)
    print('session_updated')
    return session







#qare1 proj   join quater-branch
# account_sid='**********************************'
# auth_token='ca7750c67b3bf380a956a33e9a167de6'

#qare3 proj   join subject-typical
# account_sid='**********************************'
# auth_token='08ad6f044d6546f3484e759843eed3a1'

#qare4 proj    join labor-eleven
# account_sid='**********************************'
# auth_token='f7c4bd715d85648879a7eedd28fcdebe'

# qare5 proj
# account_sid = '**********************************'
# auth_token = '4bd8bd6ffaae24486a07e1b3d0394ad9'