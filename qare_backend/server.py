from fastapi import <PERSON><PERSON><PERSON>, Request, File, UploadFile, Form, BackgroundTasks
from fastapi.responses import JSONResponse
from starlette.middleware.cors import CORSMiddleware
from chatbots import MetaData_agent, Doc_agent, Differentials_agent, generate_report, edit_report, Differentials_ranking_agent, generate_medical_summary, generate_discharge_summary_from_documents
from pydantic import BaseModel, Extra
from deepgram import DeepgramClient, PrerecordedOptions, FileSource
import os
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from typing import Dict, List, Optional
import json
from whatsapp import send_remider,send_ques,send_message,update_chat_string
from firebase import save_new_patient_report
import redis
from datetime import datetime
import time
import asyncio


# from chatbots import

r = redis.Redis(
    host='redis-10760.crce179.ap-south-1-1.ec2.redns.redis-cloud.com',
    port=10760,
    decode_responses=True,
    username="default",
    password="i3JNc2YMkQY6fHMUkXZJ9QfcSAC5qxVF",
)
app = FastAPI()
origins = [
    "http://localhost",  # Covers cases where port is omitted
    "https://doq-module.netlify.app",
    "https://doqdevline.netlify.app"
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins, but filter manually
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

@app.middleware("http")
async def add_cors_headers(request: Request, call_next):
    response = await call_next(request)
    origin = request.headers.get("origin")
    if origin and (origin.startswith("http://localhost") or origin in origins):
        response.headers["Access-Control-Allow-Origin"] = origin
    return response

DEEPGRAM_API_KEY = "****************************************"
deepgram = DeepgramClient(DEEPGRAM_API_KEY)




class Template(BaseModel):
    class Config:
        extra = Extra.allow


@app.get("/")
def read_root():
    return {"Hello": "World"}

@app.post("/meta-agent")
async def receive_data(data: Template):
    print("Received data ----------\n", data)
    response = MetaData_agent(str(data))
    print('Pushed data ----------\n', response)

    return {
        "question": response.question,
        "choices": response.choices,
        "question_type": response.question_type,
        "end_convo": response.end_convo,
    }

@app.post("/doc-agent")
async def receive_data(data: Template):
    print("Received data for Doc ----------\n")
    # print(str(data.chatHistory['chatHistory']), str(data.differentials['differentials']))
    print(data)
    response = Doc_agent(str(data.chatHistory['chatHistory']), str(data.differentials['differentials']))
    print('Pushed data for Doc ----------\n', response)

    return {
        "question": response.question,
        "choices": response.choices,
        "question_type": response.question_type,
        "end_convo": False,
    }


@app.post('/differentials_ranking')
async def receive_data(data: Template):
    print("Received data for Differentials ----------\n", str(data.chatHistory))
    response = Differentials_ranking_agent(str(data.chatHistory))
    print('Pushed data for Differentials ----------\n', response)

    return {
        "differentials": response.differentials,
        "ranking": response.ranking,
    }

@app.post('/report')
async def receive_data(data: Template):
    print("Received data for report ----------\n", data)
    response = generate_report(str(data.chatHistory['chatHistory']), str(data.differentials['differentials']))
    print('Pushed data for report ----------\n', response)

    return {
        "report": response,
    }

# @app.post('/get_info')
# async def receive_data(data: Template):
#     print("Received data for get_info ----------\n", (data))
#     response = get_info_agent(str(data))
#     return {
#         "response": response,
#     }

@app.post('/report_edit')
async def receive_data(data: Template):
    print("Received data for edit report ----------\n", str(data))
    response = edit_report(str(data.pastReport['pastReport']), str(data.pastReport['commands']))
    print('Pushed data for dit report  ----------\n', response)

    return {
        "report": response,
    }

@app.post("/upload_audio")
async def worker_audio(file: UploadFile = File(...), lang: str = Form(None), summarize: bool = Form(True)):
    try:
        print(lang)
        print(summarize)
        temp_filename = f"temp_{file.filename}"
        with open(temp_filename, "wb") as buffer:
            buffer.write(await file.read())
        with open(temp_filename, "rb") as audio_file:
            buffer_data = audio_file.read()

        payload: FileSource = {
            "buffer": buffer_data,
        }

        if lang == "en":
            options = PrerecordedOptions(
            model="nova-3",
            language="en",
        )
        else:
            options = PrerecordedOptions(
                model="nova-2",
                # detect_language=True
                language="hi"
            )

        response = deepgram.listen.rest.v("1").transcribe_file(payload, options)
        transcript = response.results.channels[0].alternatives[0].transcript
        print("transcript: ", transcript)
        print(type(transcript))

        if summarize:
            notes = generate_medical_summary(transcript)
            print(notes)
        else:
            notes = ""

        os.remove(temp_filename)

        return {"status": "success", "transcript": transcript, "notes": notes}

    except Exception as e:
        return {"status": "error", "error": str(e)}


# Sessions for mobile-web communication
sessions: Dict[str, Dict[str, WebSocket]] = {}

# IPD WebSocket connections
ipd_connections: Dict[str, List[WebSocket]] = {}
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    session_id = None
    role = None
    try:
        # Receive initial message as text and parse it
        init_data_raw = await websocket.receive_text()
        try:
            init_data = json.loads(init_data_raw)
            session_id = init_data["session_id"]
            role = init_data["role"]
        except json.JSONDecodeError:
            print(f"⚠️ Received invalid initial JSON: {init_data_raw}")
            return  # Close connection if initial message isn't valid JSON

        if session_id not in sessions:
            sessions[session_id] = {}

        sessions[session_id][role] = websocket
        print(f"🔗 {role} joined session {session_id}")
        print(f"Current sessions: {sessions.keys()}, Roles for {session_id}: {sessions[session_id].keys()}")
        # Send status update to web if connected
        if role == "mobile" and "web" in sessions[session_id]:
            status_update = json.dumps({"status": "mobile_connected"})
            await sessions[session_id]["web"].send_text(status_update)
            print(f"📤 Sent status update to web: {status_update}")

        elif role == "web" and "mobile" not in sessions[session_id]:
            status_update = json.dumps({"status": "mobile_not_connected"})
            await websocket.send_text(status_update)
            print(f"📤 Informed web that mobile is not connected: {status_update}")

        while True:
            try:
                # Receive data (text or bytes)
                data = await websocket.receive()

                # Handle binary data (audio) from mobile
                if role == "mobile" and "bytes" in data:
                    audio_data = data["bytes"]
                    # async with aiofiles.open(f'recording_{session_id}.wav', "wb") as audio_file:
                    #     await audio_file.write(audio_data)
                    # print(f"💾 Saved {len(audio_data)} bytes to recording_{session_id}.wav")
                    if "web" in sessions[session_id]:
                        await sessions[session_id]["web"].send_bytes(audio_data)
                        print(f"📤 Sent {len(audio_data)} bytes to web client")

                # Handle text data (status from mobile or commands from web)
                elif "text" in data:
                    text_data = data["text"]
                    try:
                        text_json = json.loads(text_data)

                        if role == "mobile" and "status" in text_json:
                            # Forward status updates to web
                            if "web" in sessions[session_id]:
                                await sessions[session_id]["web"].send_text(text_data)
                                print(f"📤 Forwarded status to web: {text_data}")

                        elif role == "web" and "command" in text_json:
                            # Forward commands to mobile
                            if "mobile" in sessions[session_id]:
                                await sessions[session_id]["mobile"].send_text(text_data)
                                print(f"📤 Sent command to mobile: {text_data}")
                            else:
                                print(f"📥 Received from web (no mobile connected): {text_data}")
                        else:
                            print(f"📥 Received valid JSON from {role}: {text_data}")
                    except json.JSONDecodeError:
                        # Handle non-JSON text (e.g., "ping")
                        print(f"📥 Received non-JSON text from {role}: {text_data}")

            except WebSocketDisconnect:
                print(f"❌ {role} disconnected from session {session_id}")
                break

    except WebSocketDisconnect as e:
        print(f"❌ {role} disconnected from session {session_id} (reason: {e})")
    except Exception as e:
        print(f"⚠️ Unexpected error for {role} in session {session_id}: {e}")
    finally:
        if session_id and role and session_id in sessions and role in sessions[session_id]:
            del sessions[session_id][role]
            print(f"🗑️ Removed {role} from session {session_id}")

            # Send status update to web if mobile disconnects
            if role == "mobile" and "web" in sessions[session_id]:
                status_update = json.dumps({"status": "mobile_disconnected"})
                await sessions[session_id]["web"].send_text(status_update)
                print(f"📤 Sent status update to web: {status_update}")
            if not sessions[session_id]:
                del sessions[session_id]
                print(f"🗑️ Session {session_id} deleted")


# PatientNumber is whatsapp:+{countrycode}{phone}
def format_date_for_firebase():
    now = datetime.now()
    return str(now.strftime("%d_%b_%Y_%H_%M_%S"))

@app.post("/ping_patient_pre_charting")
async def worker_ping_patient_pre_charting(data: Template):

    try:
        patient_id = data.patientId
        doc_id = data.doc_id
        Patientdata = data.patientData
        PATIENT_NUMBER = f"whatsapp:+{Patientdata['CountryCode']}{Patientdata['Phone']}"
        send_remider(PATIENT_NUMBER, Patientdata)
        session_data = {
            "doc_id": doc_id,
            "Patientdata": Patientdata,
            "patient_id": patient_id,
            "report_key": format_date_for_firebase(),
            "chat_history": {'question': [], 'choice': [], 'question_type': [], 'end_convo': False},
            "convo_state": "start",
            'chat_string': '',
            'differentials': ''
        }

        redis_key = f"session:{PATIENT_NUMBER}"
        r.set(redis_key, json.dumps(session_data), ex=1800)
        return {"status": "success"}

    except Exception as e:
        print(e)
        return {"status": "error", "error": str(e)}


@app.post("/twilio-whatsapp-webhook")
async def handle_twilio_webhook(request: Request):

    form = await request.form()
    from_number = form.get("From")
    message_body = form.get("Body")
    conversation_sid = form.get("ConversationSid")

    print(f"[Incoming WhatsApp Reply] From: {from_number}, Body: {message_body}, Conversation SID: {conversation_sid}")
    redis_key = f"session:{from_number}"
    session_json = r.get(redis_key)
    session = json.loads(session_json)

    if message_body.lower() == "start":
        access_data = session["Patientdata"]
        response = MetaData_agent(str(access_data))
        send_ques(from_number, response.question, response.choices, response.question_type)
        session['chat_history']['question'] = [response.question]
        session['chat_history']['choice'] = [response.choices]
        session['chat_history']['question_type'] = [response.question_type]
        session['chat_history']['end_convo'] = response.end_convo
        session['chat_string'] = f'{access_data}'
        session['convo_state'] = "meta"
        r.set(redis_key, json.dumps(session), ex=1800)

    elif session["convo_state"] == "meta" :
        session = update_chat_string(session,message_body,from_number)

        if len(session['chat_history']['question']) > 3 or session['chat_history']['end_convo']:
            print('meta end')
            session['convo_state'] = "doc"
            differentials = Differentials_agent(str(session['chat_string']))
            session['differentials'] = differentials

            response = Doc_agent(str(session['chat_string']), differentials)
            send_ques(from_number, response.question, response.choices, response.question_type)
            session['chat_history']['question'].append(response.question)
            session['chat_history']['choice'].append(response.choices)
            session['chat_history']['question_type'].append(response.question_type)
            r.set(redis_key, json.dumps(session), ex=1800)
            return


        response = MetaData_agent(str(session['chat_string']))
        send_ques(from_number, response.question, response.choices, response.question_type)
        session['chat_history']['question'].append(response.question)
        session['chat_history']['choice'].append(response.choices)
        session['chat_history']['question_type'].append(response.question_type)
        session['chat_history']['end_convo'] = response.end_convo
        r.set(redis_key, json.dumps(session), ex=1800)


    elif session["convo_state"] == "doc":

        session = update_chat_string(session,message_body,from_number)


        if len(session['chat_history']['question']) > 6 or session['chat_history']['end_convo']:
            print('doc end')
            session['convo_state'] = "end"
            report = generate_report(str(session['chat_string']), session['differentials'])

            report_dict = report.model_dump()

            save_new_patient_report(session['doc_id'], session['patient_id'],  session['report_key'], report_dict)

            send_message(from_number, "Thank you for your responses. Your Precounselling report is shared with the doctor.")

            r.delete(redis_key)
            return

        response = Doc_agent(str(session['chat_string']), session['differentials'])
        send_ques(from_number, response.question, response.choices, response.question_type)
        session['chat_history']['question'].append(', '+response.question)
        session['chat_history']['choice'].append(response.choices)
        session['chat_history']['question_type'].append(response.question_type)
        r.set(redis_key, json.dumps(session), ex=1800)


# Function to get upload directory path
def get_upload_dir():
    upload_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "uploads", "ipd")
    os.makedirs(upload_dir, exist_ok=True)
    return upload_dir


# Helper function to send WebSocket updates to all connected clients for an upload
async def send_ipd_status_update(upload_id: str, status_data: dict):
    """Send status updates to all WebSocket clients connected for this upload_id"""
    if upload_id in ipd_connections and ipd_connections[upload_id]:
        # Create a copy of the list to avoid modification during iteration
        connections = ipd_connections[upload_id].copy()
        for websocket in connections:
            try:
                await websocket.send_json(status_data)
                print(f"📤 Sent status update to client for upload {upload_id}")
            except Exception as e:
                print(f"⚠️ Error sending update to WebSocket for upload {upload_id}: {str(e)}")
                # Connection might be closed, remove it from the list
                if websocket in ipd_connections[upload_id]:
                    ipd_connections[upload_id].remove(websocket)


async def process_files(upload_id: str):
    """Background task to simulate file processing"""
    try:
        print(f"Starting processing for upload {upload_id}")
        upload_dir = get_upload_dir()
        upload_path = os.path.join(upload_dir, upload_id)
        metadata_path = os.path.join(upload_path, "metadata.json")

        # Read current metadata
        with open(metadata_path, "r") as f:
            metadata = json.load(f)

        # Update status to processing
        print(f"Updating status to processing for upload {upload_id}")
        await asyncio.sleep(3)  # Wait for 3 seconds
        metadata["processing_status"] = "processing"

        # Save updated metadata
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)
        print(f"Saved processing status for upload {upload_id}")

        # Send WebSocket update
        await send_ipd_status_update(upload_id, {
            "status": "success",
            "processing_status": "processing",
            "upload_id": upload_id,
            "message": "Processing files..."
        })

        # Simulate processing
        await asyncio.sleep(3)  # Wait for 3 more seconds

        # Update status to completed and add sample discharge summary
        print(f"Updating status to completed for upload {upload_id}")
        metadata["processing_status"] = "completed"
        metadata["discharge_summary"] = {
            "patient_uhid": "UH12345678",
            "patient_name": "John Doe",
            "patient_age": "45",
            "patient_gender": "Male",
            "admission_number": "ADM-2023-001",
            "admission_datetime": "2023-05-15T10:30:00",
            "patient_address": "123 Main St, Anytown, USA",
            "discharge_status": "Normal",
            "treating_consultant": "Dr. Jane Smith",
            "consultant_specialty": "Cardiology",
            "provisional_diagnosis": "Acute Myocardial Infarction",
            "presenting_complaints": [
                "Chest pain for 2 hours",
                "Shortness of breath",
                "Sweating"
            ],
            "reason_for_admission": "Acute chest pain with ECG changes",
            "physical_examination": [
                "BP: 160/90 mmHg",
                "Pulse: 110/min",
                "Respiratory Rate: 24/min"
            ],
            "general_findings": [
                "Heart: S1, S2 normal, no murmurs",
                "Lungs: Clear",
                "Abdomen: Soft, non-tender"
            ],
            "investigations_summary": [
                "ECG: ST elevation in leads II, III, aVF",
                "Troponin I: Elevated",
                "Echocardiogram: RWMA in inferior wall"
            ],
            "hospital_course": "Patient was admitted with acute inferior wall MI. Underwent primary PCI with stent placement to RCA. Post-procedure course was uneventful.",
            "treatment_summary": [
                "Primary PCI with stent to RCA",
                "Dual antiplatelet therapy",
                "Beta-blockers",
                "ACE inhibitors"
            ],
            "surgery_performed": False,
            "surgery_notes": None,
            "anesthetist_name": None,
            "anesthesia_type": None,
            "final_diagnosis": "Inferior Wall Myocardial Infarction",
            "discharge_prescription": [
                "Aspirin 75mg once daily",
                "Clopidogrel 75mg once daily",
                "Metoprolol 25mg twice daily",
                "Ramipril 5mg once daily",
                "Atorvastatin 40mg once daily"
            ],
            "discharge_advice": [
                "Avoid strenuous activity for 2 weeks",
                "Follow cardiac rehabilitation program",
                "Low salt, low fat diet",
                "Quit smoking"
            ],
            "follow_up_date": "2023-06-15",
            "emergency_contact": "Emergency Cardiology: ************",
            "doctor_signatory": "Dr. Jane Smith, MD, Cardiology",
            "patient_or_attendant_signatory": "John Doe"
        }

        # Save final metadata
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)
        print(f"Saved completed status for upload {upload_id}")

        # Send WebSocket update for completion
        await send_ipd_status_update(upload_id, {
            "status": "success",
            "processing_status": "completed",
            "upload_id": upload_id,
            "message": "Processing completed successfully",
            "discharge_summary": metadata["discharge_summary"]
        })

    except Exception as e:
        print(f"Error in process_files: {str(e)}")
        # Try to update metadata with error status if possible
        try:
            with open(metadata_path, "r") as f:
                metadata = json.load(f)
            metadata["processing_status"] = "error"
            metadata["error_message"] = str(e)
            with open(metadata_path, "w") as f:
                json.dump(metadata, f, indent=2)

            # Send WebSocket update for error
            await send_ipd_status_update(upload_id, {
                "status": "error",
                "processing_status": "error",
                "upload_id": upload_id,
                "message": f"Error during processing: {str(e)}"
            })
        except Exception as inner_e:
            print(f"Failed to update error status: {str(inner_e)}")


@app.post("/upload_ipd_files")
async def upload_ipd_files(
    background_tasks: BackgroundTasks,
    file0: Optional[UploadFile] = File(None),
    file1: Optional[UploadFile] = File(None),
    file2: Optional[UploadFile] = File(None),
    file3: Optional[UploadFile] = File(None),
    file4: Optional[UploadFile] = File(None),
    file5: Optional[UploadFile] = File(None),
    file6: Optional[UploadFile] = File(None),
    file7: Optional[UploadFile] = File(None),
    file8: Optional[UploadFile] = File(None),
    file9: Optional[UploadFile] = File(None),
    timestamp: Optional[str] = Form(None),
    source: Optional[str] = Form("web_upload")
):
    """Upload multiple files for IPD processing."""
    try:
        # Get upload directory
        upload_dir = get_upload_dir()

        # Collect all uploaded files
        uploaded_files = []
        for i in range(10):
            file_var = locals()[f'file{i}']
            if file_var is not None and file_var.filename:
                uploaded_files.append(file_var)

        # Check if any files were uploaded
        if not uploaded_files:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "No files were uploaded"}
            )

        # Create a unique directory for this upload
        upload_timestamp = timestamp or datetime.now().isoformat()
        upload_id = f"{int(time.time())}_{upload_timestamp.replace(':', '-')}"
        upload_path = os.path.join(upload_dir, upload_id)
        os.makedirs(upload_path, exist_ok=True)

        # Process each file
        file_responses = []
        for file in uploaded_files:
            # Create a safe filename
            filename = file.filename
            safe_filename = "".join(c if c.isalnum() or c in "._- " else "_" for c in filename)
            file_path = os.path.join(upload_path, safe_filename)

            # Save the file
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)

            # Create response for this file
            file_size = len(content)
            file_responses.append({
                "filename": filename,
                "size": file_size,
                "content_type": file.content_type or "application/octet-stream",
                "saved_path": file_path
            })

        # Create metadata file
        metadata = {
            "upload_id": upload_id,
            "timestamp": upload_timestamp,
            "source": source,
            "processing_status": "uploading",  # Initial status
            "file_count": len(uploaded_files),
            "files": [
                {
                    "filename": resp["filename"],
                    "size": resp["size"],
                    "content_type": resp["content_type"],
                    "saved_path": resp["saved_path"]
                }
                for resp in file_responses
            ]
        }

        # Save metadata to file
        metadata_path = os.path.join(upload_path, "metadata.json")
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)

        # Send initial WebSocket update if any clients are already connected
        # (though this is unlikely at this point)
        if upload_id in ipd_connections and ipd_connections[upload_id]:
            await send_ipd_status_update(upload_id, {
                "status": "success",
                "processing_status": "uploading",
                "upload_id": upload_id,
                "message": f"Successfully uploaded {len(uploaded_files)} files"
            })

        # Start background processing
        background_tasks.add_task(process_files, upload_id)

        # Return success response
        return {
            "status": "success",
            "message": f"Successfully uploaded {len(uploaded_files)} files",
            "upload_id": upload_id,
            "files": [
                {
                    "filename": resp["filename"],
                    "size": resp["size"],
                    "content_type": resp["content_type"]
                }
                for resp in file_responses
            ]
        }

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


@app.get("/ipd_upload/{upload_id}")
async def get_ipd_upload(upload_id: str):
    """Get details of a specific IPD upload."""
    try:
        # Get upload directory
        upload_dir = get_upload_dir()

        # Read from file
        upload_path = os.path.join(upload_dir, upload_id)
        if not os.path.isdir(upload_path):
            return JSONResponse(
                status_code=404,
                content={"status": "error", "message": f"Upload {upload_id} not found"}
            )

        metadata_path = os.path.join(upload_path, "metadata.json")
        if not os.path.exists(metadata_path):
            return JSONResponse(
                status_code=404,
                content={"status": "error", "message": f"Metadata for upload {upload_id} not found"}
            )

        with open(metadata_path, "r") as f:
            metadata = json.load(f)

        return {
            "status": "success",
            "upload": metadata
        }

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


@app.get("/ipd_uploads")
async def list_ipd_uploads():
    """List all IPD uploads."""
    try:
        # Get upload directory
        upload_dir = get_upload_dir()

        uploads = []
        for upload_id in os.listdir(upload_dir):
            upload_path = os.path.join(upload_dir, upload_id)
            if os.path.isdir(upload_path):
                metadata_path = os.path.join(upload_path, "metadata.json")
                if os.path.exists(metadata_path):
                    with open(metadata_path, "r") as f:
                        metadata = json.load(f)
                        uploads.append(metadata)

        return {
            "status": "success",
            "uploads": uploads
        }

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


@app.websocket("/ws/ipd/{upload_id}")
async def ipd_websocket_endpoint(websocket: WebSocket, upload_id: str):
    """WebSocket endpoint for IPD status updates."""
    await websocket.accept()
    print(f"🔗 Client connected to IPD WebSocket for upload {upload_id}")

    try:
        # Add this connection to the list for this upload_id
        if upload_id not in ipd_connections:
            ipd_connections[upload_id] = []
        ipd_connections[upload_id].append(websocket)

        # Send initial status update
        try:
            # Get current status from metadata file
            upload_dir = get_upload_dir()
            upload_path = os.path.join(upload_dir, upload_id)
            metadata_path = os.path.join(upload_path, "metadata.json")

            if os.path.exists(metadata_path):
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)

                # Send current status
                await websocket.send_json({
                    "status": "success",
                    "upload_id": upload_id,
                    "processing_status": metadata.get("processing_status", "unknown"),
                    "message": f"Current status: {metadata.get('processing_status', 'unknown')}"
                })
            else:
                await websocket.send_json({
                    "status": "error",
                    "message": f"Upload {upload_id} not found"
                })
        except Exception as e:
            print(f"⚠️ Error sending initial status: {str(e)}")
            await websocket.send_json({
                "status": "error",
                "message": f"Error retrieving status: {str(e)}"
            })

        # Keep connection alive until client disconnects
        while True:
            # Wait for any message from client (ping/pong or close)
            data = await websocket.receive_text()
            print(f"📥 Received from IPD WebSocket client: {data}")

            # You could handle specific commands here if needed
            if data.lower() == "ping":
                await websocket.send_text("pong")

    except WebSocketDisconnect:
        print(f"❌ Client disconnected from IPD WebSocket for upload {upload_id}")
    except Exception as e:
        print(f"⚠️ Error in IPD WebSocket: {str(e)}")
    finally:
        # Remove this connection from the list
        if upload_id in ipd_connections and websocket in ipd_connections[upload_id]:
            ipd_connections[upload_id].remove(websocket)
            print(f"🗑️ Removed client from IPD WebSocket connections for upload {upload_id}")

        # Clean up empty lists
        if upload_id in ipd_connections and not ipd_connections[upload_id]:
            del ipd_connections[upload_id]
            print(f"🗑️ Removed empty connection list for upload {upload_id}")




def start():
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
    start()


















# @app.post('/differentials')
# async def receive_data(data: Template):
#     print("Received data for Differentials ----------\n", str(data.chatHistory))
#     response = Differentials_agent(str(data.chatHistory))
#     print('Pushed data for Differentials ----------\n', response)

#     return {
#         "differentials": response,
#     }


# @app.post("/patient_chat")
# async def receive_data(data: Template):
#     print("Received data for PatChat ----------\n")
#     print(data.chatHistory, data.patientData)
#     response = PatChat_agent(data.chatHistory, data.patientData)
#     print('Pushed data for PatChat ----------\n', response)
#     return {
#         "response": response,
#     }

