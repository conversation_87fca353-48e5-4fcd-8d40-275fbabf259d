fastapi==0.115.8
groq==0.18.0
instructor==1.7.2
pydantic==2.10.6
requests==2.32.3
uvicorn==0.34.0
deepgram-sdk==3.10.1
python-multipart==0.0.20
openai==1.52.0
Pillow==10.3.0

# FastAPI dependencies
starlette<0.46.0,>=0.40.0
typing-extensions>=4.8.0

# Groq dependencies
anyio<5,>=3.5.0
distro<2,>=1.7.0
httpx<1,>=0.23.0
sniffio

# Instructor dependencies
aiohttp<4.0.0,>=3.9.1
docstring-parser<1.0,>=0.16
jinja2<4.0.0,>=3.1.4
jiter<0.9,>=0.6.1
openai<2.0.0,>=1.52.0
pydantic-core<3.0.0,>=2.18.0
rich<14.0.0,>=13.7.0
tenacity<10.0.0,>=9.0.0
typer<1.0.0,>=0.9.0

# Pydantic dependencies
annotated-types>=0.6.0

# Requests dependencies
charset-normalizer<4,>=2
idna<4,>=2.5
urllib3<3,>=1.21.1
certifi>=2017.4.17

# Uvicorn dependencies
click>=7.0
h11>=0.8

# Deepgram dependencies
websockets>=12.0
dataclasses-json>=0.6.3
aiofiles>=23.2.1
aenum>=3.1.0
deprecation>=2.1.0

# Aiohttp dependencies
aiohappyeyeballs>=2.3.0
aiosignal>=1.1.2
attrs>=17.3.0
frozenlist>=1.1.1
multidict<7.0,>=4.5
propcache>=0.2.0
yarl<2.0,>=1.17.0

# Dataclasses-json dependencies
marshmallow<4.0.0,>=3.18.0
typing-inspect<1,>=0.4.0

# Other dependencies
packaging
httpcore==1.*
MarkupSafe>=2.0
tqdm>4
markdown-it-py>=2.2.0
pygments<3.0.0,>=2.13.0
shellingham>=1.3.0
mdurl~=0.1
mypy-extensions>=0.3.0

twilio==9.5.0
firebase-admin==6.7.0
redis==5.2.1
boto3==1.34.69