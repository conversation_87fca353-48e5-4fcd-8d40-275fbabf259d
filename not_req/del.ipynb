import redis
import json
r = redis.Redis(
    host='redis-10760.crce179.ap-south-1-1.ec2.redns.redis-cloud.com',
    port=10760,
    decode_responses=True,
    username="default",
    password="i3JNc2YMkQY6fHMUkXZJ9QfcSAC5qxVF",
)

redis_key = f"session:whatsapp:+************"
session_json = r.get(redis_key)
session = json.loads(session_json)

session

# Configure content - List Picker
# Body
# Ques: {{1}}
# 7/1024
# Sample content for variable {{1}}
# Unset
# List button text
# Select Your Response
# 20/20
# List item
# Item name
# {{2}}
# 1/24
# Sample content for variable {{2}}
# Unset
# Item ID
# 2
# 1/200
# Item description
# 0/72
# List item
# Item name
# {{3}}
# 1/24
# Sample content for variable {{3}}
# Unset
# Item ID
# 3
# 1/200
# Item description
# 0/72
# List item
# Item name
# {{4}}
# 1/24
# Sample content for variable {{4}}
# Unset
# Item ID
# 4
# 1/200
# Item description

from twilio.rest import Client
import json 

account_sid='**********************************'
auth_token='22990fbfa01c0071a56511c25f403f8e'
client = Client(account_sid, auth_token)


Ques = "Which department do you need help with?"
choices = ['sales',' ',' ']

message = client.messages.create(
    from_='whatsapp:+************',
    to='whatsapp:+************',
    content_sid='HXd36d74a7c251254e1c85ebebf991d27b',
    content_variables=json.dumps({
        "1": Ques,  # Main question
        **{f"{i}": choice for i, choice in enumerate(choices, start=2)}
    })
)

print(message.sid)

from twilio.rest import Client
import json

account_sid = '**********************************'
auth_token = '22990fbfa01c0071a56511c25f403f8e'
client = Client(account_sid, auth_token)

Ques = "Which department do you need help with?"
choices = ['one', 'two', 'three', 'four', 'five']  

# Example: use different content_sid for different number of options
template_map = {
    2: 'HXafb1f526932837c89a8988c576759c77',
    3: 'HX659cf101288d2f3e31d8ec305c3515ff',
    4: 'HX397e014ea45e3b8f6c5bce1ffadf2cea', 
    5: 'HX6c60a124e6e7901a1a0af53af25278c5',
    6: 'HX53c9e1f213afc5a811c261c2ff1e02be',
}
template_to_use = template_map[len(choices)]

message = client.messages.create(
    from_='whatsapp:+***********',
    to='whatsapp:+************',
    content_sid=template_to_use,
    content_variables=json.dumps({
        "1": Ques,
        **{f"{i}": choice for i, choice in enumerate(choices, start=2)}
    })
)

print(message.sid)


import openai
from PIL import Image

# Step 1: Set your OpenAI API key
openai.api_key = "********************************************************************************************************************************************************************"

# Step 2: Function to process the image and generate summary
def generate_summary_from_prescription(image_path):
    # Load image using Pillow
    image = Image.open(image_path)

    # Call GPT-4 Vision model
    response = openai.chat.completions.create(
        model="gpt-4-vision-preview",
        messages=[
            {"role": "system", "content": "You're a helpful assistant that extracts medical information from doctor prescriptions."},
            {"role": "user", "content": [
                {"type": "text", "text": "Please read this doctor's handwritten prescription and performt OCR"},
                {"type": "image_url", "image_url": {
                    "url": "data:image/jpeg;base64," + image_to_base64(image),
                    "detail": "high"
                }}
            ]}
        ],
        max_tokens=700,
    )

    return response.choices[0].message.content

# Helper to convert image to base64
import base64
from io import BytesIO

def image_to_base64(image):
    buffered = BytesIO()
    image.save(buffered, format="JPEG")
    return base64.b64encode(buffered.getvalue()).decode("utf-8")


image_path = "/home/<USER>/thrash/not_req/docpres1.jpg"  # change this to your image path
result = generate_summary_from_prescription(image_path)
print("\n🧾 Extracted Summary:\n")
print(result)


from ocr_utils import process_document

# Process a PDF or image file
result = process_document("/home/<USER>/thrash/not_req/docpres1.jpg")
if not result.startswith("Error:"):
    print("Extracted text:", result)
else:
    print("Processing failed:", result)

response = openai_client.chat.completions.create(
                    model="gpt-4o",  # Using GPT-4o which has vision capabilities
                    messages=[
                        {
                            "role": "system",
                            "content": "You are a precise OCR system. Extract all text from the image exactly as it appears."
                        },
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": "Extract all text from this medical document image."},
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{base64_image}",
                                        "detail": "high"
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens=1500,
                )







model_id = "HuggingFaceM4/idefics2-8b"
processor = AutoProcessor.from_pretrained(model_id, cache_dir="/Data/OCR")
model = AutoModelForVision2Seq.from_pretrained(
    model_id,
    cache_dir="/Data/OCR",
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto"  # This will automatically handle GPU/CPU placement
)

model_id = "HuggingFaceM4/idefics2-8b"
processor = AutoProcessor.from_pretrained(model_id, cache_dir="/Data/OCR")
model2 = Idefics2ForConditionalGeneration.from_pretrained(
    model_id,
    cache_dir="/Data/OCR",
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto"  # This will automatically handle GPU/CPU placement
)

import os
import torch
from transformers import AutoProcessor, AutoModelForVision2Seq
from transformers import Idefics2ForConditionalGeneration
from PIL import Image



from PIL import Image

image = Image.open(image_path)

# Create prompt
prompt = "<image>\nExtract all text from this image."

# Process inputs
inputs = processor(prompt, image, return_tensors="pt").to(model.device)

# Generate text
with torch.no_grad():
    generated_ids = model.generate(
        **inputs,
        max_new_tokens=512,
        do_sample=False
    )

# Decode the generated text
generated_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]

print("\nExtracted Text:")
print(generated_text)

import os
os.environ['OPENAI_API_KEY'] = "********************************************************************************************************************************************************************"  # Replace with your actual OpenAI API key

import base64
from openai import OpenAI

client = OpenAI()

# Function to encode the image
def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")

# Path to your image
image_path = "/home/<USER>/thrash/not_req/1.webp"

# Getting the Base64 string
base64_image = encode_image(image_path)

# Constructing the data URL
image_data_url = f"data:image/jpeg;base64,{base64_image}"

# Using GPT-4o for OCR
response = client.chat.completions.create(
    model="gpt-4o",
    messages=[
        {
            "role": "user",
            "content": [
                { "type": "text", "text": "Extract all text from this lab report image. Present it cleanly." },
                { "type": "image_url", "image_url": { "url": image_data_url } },
            ]
        }
    ],
    max_tokens=2000
)

# Print the extracted text
print(response.choices[0].message.content)


import base64
from openai import OpenAI

client = OpenAI()

def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")

image_path = "/home/<USER>/thrash/not_req/duggu-reporter-2.jpg"
base64_image = encode_image(image_path)
image_data_url = f"data:image/jpeg;base64,{base64_image}"

# GPT-4o OCR and structured lab result extraction
response = client.chat.completions.create(
    model="gpt-4o",
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": (
                        "You are a medical assistant. Extract lab test results from this image. "
                        "Return the output as a JSON array. Each object should contain:\n"
                        "- test_name: string\n"
                        "- value: number or string\n"
                        "- unit: string\n"
                        "- reference_range: string\n"
                        "- is_abnormal: true or false based on normal range comparison\n"
                        "- notes: optional explanation if any value is unreadable or estimated.\n\n"
                        "If any field is missing in the image, leave it as null. Only include actual test results."
                    )
                },
                { "type": "image_url", "image_url": { "url": image_data_url } }
            ]
        }
    ],
    max_tokens=2000
)

# Print the structured JSON output
print(response.choices[0].message.content)


import base64
from openai import OpenAI
from pdf2image import convert_from_path
from io import BytesIO

client = OpenAI()

def convert_pdf_to_images(pdf_path):
    return convert_from_path(pdf_path, dpi=200)

def encode_image_from_pil(pil_image):
    buffered = BytesIO()
    pil_image.save(buffered, format="JPEG", quality=95, optimize=True)
    return base64.b64encode(buffered.getvalue()).decode("utf-8")

def build_image_messages_from_pdf(pdf_path):
    images = convert_pdf_to_images(pdf_path)
    messages = []
    
    for i, image in enumerate(images):
        base64_img = encode_image_from_pil(image)
        messages.append({
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{base64_img}",
                "detail": "high"
            }
        })
    
    return messages

pdf_path = "/home/<USER>/thrash/not_req/temp_7aa8f0d5e6216dab.pdf"
image_messages = build_image_messages_from_pdf(pdf_path)

# Prompt asking GPT-4o to extract lab data as structured JSON
system_prompt = {
    "type": "text",
    "text": (
        "You are a medical assistant helping doctors analyze patient lab reports. "
        "Extract all visible text from the images, then identify key medical information, abnormal values, "
        "and clinically significant findings. Format your response in two parts:\n\n"
        "1) EXTRACTED TEXT: containing all visible text from the images, and\n"
        "2) ANALYSIS: containing a concise, bulleted list of the most important findings."
    )
}

response = client.chat.completions.create(
    model="gpt-4.1",
    messages=[
        {
            "role": "user",
            "content": [system_prompt] + image_messages
        }
    ],
    temperature = 0,
    max_tokens=3000
)

# Output the parsed lab results
print(response.choices[0].message.content)


pdf_path = "/home/<USER>/thrash/not_req/temp_7aa8f0d5e6216dab.pdf"
image_messages = build_image_messages_from_pdf(pdf_path)


def process_lab_reports(encoded_reports):
    """Process all encoded lab reports in one go"""
    try:
        # Prepare messages for OpenAI
        messages = []

        user_content = []

        # Add all encoded images
        for report in encoded_reports:
            for encoded_image in report["encoded_images"]:
                user_content.append({
                    "content": f"data:{report['content_type']};base64,{encoded_image}"
                    })

        system_prompt = {"role": "system", "content": "You are a medical assistant helping doctors analyze patient lab reports. Your task is to extract text from medical lab reports and identify clinically relevant information. This is for legitimate medical purposes to help patients and doctors. First extract all visible text from the images, then identify key medical information, abnormal values, and clinically significant findings. Format your response in two parts: 1) EXTRACTED TEXT: containing all visible text from the images, and 2) ANALYSIS: containing a concise, bulleted list of the most important findings."}
        
        messages.append(system_prompt)

        # Create a single user message that includes both text and images
        messages.append({
            "role": "user",
            "content": user_content
        })
        from openai import OpenAI
        os.environ['OPENAI_API_KEY'] = "********************************************************************************************************************************************************************"  # Replace with your actual OpenAI API key
        model = "gpt-4o"
        client = OpenAI()

        # Call OpenAI API
        # print("The message sent to image_extract: \n", messages)
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            max_tokens=4000
        )
        print("Analysis from report(s): ", response.choices[0].message.content)
        return response.choices[0].message.content

    except Exception as e:
        raise Exception(f"Error processing lab reports: {str(e)}")