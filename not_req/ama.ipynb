{"cells": [{"cell_type": "code", "execution_count": null, "id": "8316c6a0", "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "# Initialize Textract client\n", "textract = boto3.client('textract')\n", "\n", "# Load local image\n", "with open(\"docpres3.jpg\", \"rb\") as document:\n", "    image_bytes = document.read()\n", "\n", "# Call Textract\n", "response = textract.detect_document_text(Document={'Bytes': image_bytes})\n", "\n", "# Parse and print detected text\n", "for item in response[\"Blocks\"]:\n", "    if item[\"BlockType\"] == \"LINE\":\n", "        print(item[\"Text\"])\n"]}, {"cell_type": "code", "execution_count": null, "id": "537ac314", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ef042442", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'qare_backend'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 55\u001b[0m\n\u001b[1;32m      1\u001b[0m documents \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m      2\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m001_AdmissionNote\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;124mPatient Name: Mr. <PERSON><PERSON>\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     52\u001b[0m \u001b[38;5;124m\"\"\"\u001b[39m,\n\u001b[1;32m     53\u001b[0m }\n\u001b[0;32m---> 55\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mqare_backend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mchatbots\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m generate_discharge_summary_from_documents\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'qare_backend'"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98ddcdb6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "qare_backend", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}