from openai import OpenAI
import os
import instructor

from pydantic import BaseModel, Field
from typing import List, Optional

class Quesnare_var_type(BaseModel):
    question: str
    choices: List[str]
    end_convo: bool
    question_type : str

# Set up OpenAI client
os.environ['OPENAI_API_KEY'] = "********************************************************************************************************************************************************************"  # Replace with your actual OpenAI API key
client = OpenAI()
model = "gpt-4o"
client = instructor.patch(client)

def Voice_MetaData_agent(chat):
    """
    Modified version of MetaData_agent specifically for voice calls.
    Generates questions that are more conversational and don't rely on multiple choice options.
    """
    print('RECEIVED VOICE META AGENT')
    print(chat)
    prompt = '''You are an experienced history-taking nurse conducting a phone consultation. Your task is to collect the most relevant patient history efficiently using a conversational question flow. Your goal is to reach a diagnosis in 7 questions or fewer.

**Key Instructions:**
- Ask only one concise, clear question at a time based on the patient's previous response.
- Frame questions for a voice conversation - they should be easy to understand when heard, not read.
- Do not use multiple choice options - patients will respond freely with their own words.
- Phrase questions to elicit detailed responses, not just yes/no answers.
- Do not repeat questions.
- Minimize the number of questions while covering essential aspects:
  * Symptom Onset
  * Severity
  * Duration
  * Medical History
  * Lifestyle Factors
  * Allergies
- Avoid asking for information the patient is unlikely to know.
- When the history is complete, set "end_convo": true; otherwise, keep it false.

**Response Format:**
Return a valid JSON with the following structure:
{
  "question": "Your next question here",
  "choices": [],  // Always return an empty array for voice calls
  "question_type": "text",  // Always use "text" for voice calls
  "end_convo": false  // Set to true when history is complete
}

Stay precise and efficient. Goal: Complete history taking as efficiently as possible with a maximum of 7 questions.'''

    chat_history = [{"role": "system", "content": prompt}, {"role": "user", "content": chat}]
    response = client.chat.completions.create(
        model=model,
        messages=chat_history,
        response_model=Quesnare_var_type,
        temperature=0.1
    )
    return response


print(Voice_MetaData_agent("I have a fever and a cough"))
